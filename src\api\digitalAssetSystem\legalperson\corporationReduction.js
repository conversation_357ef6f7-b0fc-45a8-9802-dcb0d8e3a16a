import request from '@/utils/request'

// 分页查询企业压减信息表
export function getCorporationReductionList(query) {
  return request({
    url: '/zc-corporation-reduction/vPageList',
    method: 'get',
    params: query
  })
}

// 分组统计企业压减信息表
export function getCorporationReductionStat(query) {
  return request({
    url: '/zc-corporation-reduction/vStat',
    method: 'get',
    params: query
  })
}

// 新增企业压减信息
export function addCorporationReduction(data) {
  return request({
    url: '/zc-corporation-reduction/save',
    method: 'post',
    data: data
  })
}

// 修改企业压减信息
export function updateCorporationReduction(data) {
  return request({
    url: '/zc-corporation-reduction/update',
    method: 'post',
    data: data
  })
}

// 新增或修改企业压减信息(带日志)
export function saveOrUpdateCorporationReduction(data) {
  return request({
    url: '/zc-corporation-reduction/saveOrUpdLog',
    method: 'post',
    data
  })
}

// 删除企业压减信息
export function deleteCorporationReduction(ids) {
  return request({
    url: '/zc-corporation-reduction/delete',
    method: 'post',
    data: { ids: ids }
  })
}

// 通过id查询企业压减信息
export function getCorporationReductionById(id) {
  return request({
    url: `/zc-corporation-reduction/get-by-id/${id}`,
    method: 'get'
  })
}

// 提交建议
export function submitSuggestion(data) {
  return request({
    url: '/zc-corporation-reduction/saveOrUpdLog',
    method: 'post',
    data: {
      ...data,
      logDesc: `提交建议：${data.zccrEnterpriseName || '法人企业'}`
    }
  })
}

// 提交修正
export function submitCorrection(data) {
  return request({
    url: '/zc-corporation-reduction/saveOrUpdLog',
    method: 'post',
    data: {
      ...data,
      logDesc: `修正信息：${data.zccrEnterpriseName || '法人企业'}`
    }
  })
}

// 批量更新流程阶段状态
export function batchUpdateProcessStage(params) {
  return request({
    url: '/zc-corporation-reduction/batch-update-process-stage',
    method: 'post',
    params
  })
}

// 导出企业压减信息
export function exportCorporationReduction(data) {
  return request({
    url: '/zc-corporation-reduction/vExport',
    method: 'post',
    data
  })
}
